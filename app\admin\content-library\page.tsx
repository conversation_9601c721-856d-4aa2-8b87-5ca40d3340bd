'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { 
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ViewColumnsIcon,
  ListBulletIcon,
  PhotoIcon,
  VideoCameraIcon,
  DocumentIcon,
  MusicalNoteIcon,
  FolderIcon,
  TagIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { CheckIcon as CheckIconSolid } from '@heroicons/react/24/solid'

interface ContentFile {
  id: string
  filename: string
  originalName: string
  url: string
  thumbnailUrl?: string
  type: 'image' | 'video' | 'document' | 'audio' | 'other'
  mimeType: string
  size: number
  folder: string
  description?: string
  tags: string[]
  usageCount: number
  createdAt: string
  updatedAt: string
}

interface ContentLibraryStats {
  totalFiles: number
  typeBreakdown: Record<string, { count: number; totalSize: number }>
  totalSize: number
}

interface Folder {
  name: string
  fileCount: number
}

export default function ContentLibraryPage() {
  const [files, setFiles] = useState<ContentFile[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedFolder, setSelectedFolder] = useState<string>('all')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [statistics, setStatistics] = useState<ContentLibraryStats | null>(null)
  const [folders, setFolders] = useState<Folder[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  useEffect(() => {
    fetchFiles()
  }, [searchTerm, selectedType, selectedFolder, selectedTags, pagination.page])

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType !== 'all' && { type: selectedType }),
        ...(selectedFolder !== 'all' && { folder: selectedFolder }),
        ...(selectedTags.length > 0 && { tags: selectedTags.join(',') })
      })

      const response = await fetch(`/api/admin/content-library?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // Handle specific error cases
        if (response.status === 404) {
          throw new Error('Content library API not found. Please ensure the database is properly set up.')
        } else if (response.status === 500) {
          throw new Error(errorData.message || 'Database error. The content library table may not exist.')
        } else {
          throw new Error(errorData.message || `Failed to fetch files (${response.status})`)
        }
      }

      const data = await response.json()
      setFiles(data.files || [])
      setPagination(data.pagination || pagination)
      setStatistics(data.statistics || null)
      setFolders(data.folders || [])
      setAvailableTags(data.availableTags || [])
    } catch (error: any) {
      console.error('Error fetching files:', error)

      // Show more helpful error messages
      if (error.message.includes('database') || error.message.includes('table')) {
        toast.error('Content library is not set up. Please run database migrations.')
      } else if (error.message.includes('API not found')) {
        toast.error('Content library feature is not available.')
      } else {
        toast.error(error.message || 'Failed to load content library')
      }

      // Set empty state to prevent further errors
      setFiles([])
      setStatistics(null)
      setFolders([])
      setAvailableTags([])
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = event.target.files
    if (!uploadedFiles || uploadedFiles.length === 0) return

    setUploading(true)
    let successCount = 0
    let errorCount = 0

    for (const file of Array.from(uploadedFiles)) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('folder', selectedFolder !== 'all' ? selectedFolder : 'general')

        const response = await fetch('/api/admin/content-library', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          const error = await response.json().catch(() => ({}))

          if (response.status === 404) {
            throw new Error('Content library API not available')
          } else if (response.status === 500) {
            throw new Error(error.message || 'Database error during upload')
          } else {
            throw new Error(error.message || 'Upload failed')
          }
        }

        successCount++
      } catch (error: any) {
        console.error('Error uploading file:', error)
        errorCount++
        toast.error(`Failed to upload ${file.name}: ${error.message}`)
      }
    }

    if (successCount > 0) {
      toast.success(`Successfully uploaded ${successCount} file(s)`)
      fetchFiles()
    }

    if (errorCount > 0) {
      toast.error(`Failed to upload ${errorCount} file(s)`)
    }

    setUploading(false)
    // Reset file input
    event.target.value = ''
  }

  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => {
      const newSelection = prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
      
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }

  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([])
      setShowBulkActions(false)
    } else {
      setSelectedFiles(files.map(f => f.id))
      setShowBulkActions(true)
    }
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedFiles.length} file(s)?`)) return

    try {
      const response = await fetch('/api/admin/content-library/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'delete',
          fileIds: selectedFiles,
          data: { force: false }
        })
      })

      if (!response.ok) throw new Error('Failed to delete files')

      const result = await response.json()
      toast.success(`Deleted ${result.results.success} file(s)`)
      
      if (result.results.failed > 0) {
        toast.error(`Failed to delete ${result.results.failed} file(s)`)
      }

      setSelectedFiles([])
      setShowBulkActions(false)
      fetchFiles()
    } catch (error) {
      console.error('Error deleting files:', error)
      toast.error('Failed to delete files')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return PhotoIcon
      case 'video': return VideoCameraIcon
      case 'document': return DocumentIcon
      case 'audio': return MusicalNoteIcon
      default: return DocumentIcon
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'text-green-600 bg-green-100'
      case 'video': return 'text-blue-600 bg-blue-100'
      case 'document': return 'text-purple-600 bg-purple-100'
      case 'audio': return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Content Library
            </h1>
            <p className="text-gray-600 mt-2">Manage your media files and documents</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <input
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
            />
            <motion.label
              htmlFor="file-upload"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer"
            >
              <CloudArrowUpIcon className="w-5 h-5 mr-2" />
              {uploading ? 'Uploading...' : 'Upload Files'}
            </motion.label>
          </div>
        </div>

        {/* Statistics Cards */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Files</p>
                  <p className="text-2xl font-bold text-gray-900">{statistics.totalFiles.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-xl">
                  <DocumentIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Size</p>
                  <p className="text-2xl font-bold text-gray-900">{formatFileSize(statistics.totalSize)}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-xl">
                  <CloudArrowUpIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Images</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(statistics.typeBreakdown.image?.count || 0).toLocaleString()}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-xl">
                  <PhotoIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Videos</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(statistics.typeBreakdown.video?.count || 0).toLocaleString()}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-xl">
                  <VideoCameraIcon className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </motion.div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search files..."
                className="w-full pl-10 pr-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option value="all">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="document">Documents</option>
                <option value="audio">Audio</option>
                <option value="other">Other</option>
              </select>

              <select
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option value="all">All Folders</option>
                {folders.map(folder => (
                  <option key={folder.name} value={folder.name}>
                    {folder.name} ({folder.fileCount})
                  </option>
                ))}
              </select>

              {/* View Mode Toggle */}
              <div className="flex items-center bg-white/50 border border-gray-200 rounded-xl p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <ViewColumnsIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'list'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <ListBulletIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Empty State */}
        {!loading && files.length === 0 && (
          <div className="text-center py-12">
            <CloudArrowUpIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
            <p className="text-gray-600 mb-6">Upload some files to get started</p>
            <label
              htmlFor="file-upload"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer"
            >
              <CloudArrowUpIcon className="w-5 h-5 mr-2" />
              Upload Files
            </label>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6 animate-pulse">
                <div className="aspect-square bg-gray-200 rounded-xl mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        )}

        {/* Files Display */}
        {!loading && files.length > 0 && (
          <>
            {/* Bulk Actions */}
            <AnimatePresence>
              {showBulkActions && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm font-medium text-blue-800">
                        {selectedFiles.length} file(s) selected
                      </span>
                      <button
                        onClick={handleSelectAll}
                        className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {selectedFiles.length === files.length ? 'Deselect All' : 'Select All'}
                      </button>
                    </div>

                    <div className="flex items-center space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleBulkDelete}
                        className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-colors"
                      >
                        <TrashIcon className="w-4 h-4 mr-2" />
                        Delete Selected
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Grid View */}
            {viewMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <AnimatePresence>
                  {files.map((file, index) => {
                    const FileIcon = getFileIcon(file.type)
                    const isSelected = selectedFiles.includes(file.id)

                    return (
                      <motion.div
                        key={file.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ delay: index * 0.05 }}
                        className={`bg-white/60 backdrop-blur-xl rounded-2xl border transition-all duration-200 overflow-hidden group hover:shadow-lg ${
                          isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-white/20'
                        }`}
                      >
                        {/* File Preview */}
                        <div className="relative aspect-square bg-gray-100">
                          {file.thumbnailUrl ? (
                            <img
                              src={file.thumbnailUrl}
                              alt={file.originalName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <FileIcon className="w-16 h-16 text-gray-400" />
                            </div>
                          )}

                          {/* Selection Checkbox */}
                          <div className="absolute top-3 left-3">
                            <button
                              onClick={() => handleFileSelect(file.id)}
                              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                                isSelected
                                  ? 'bg-blue-500 border-blue-500 text-white'
                                  : 'bg-white border-gray-300 hover:border-blue-400'
                              }`}
                            >
                              {isSelected && <CheckIconSolid className="w-4 h-4" />}
                            </button>
                          </div>

                          {/* File Type Badge */}
                          <div className="absolute top-3 right-3">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(file.type)}`}>
                              {file.type}
                            </span>
                          </div>

                          {/* Actions Overlay */}
                          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => window.open(file.url, '_blank')}
                              className="p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                            >
                              <EyeIcon className="w-5 h-5" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const a = document.createElement('a')
                                a.href = file.url
                                a.download = file.originalName
                                a.click()
                              }}
                              className="p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                            >
                              <ArrowDownTrayIcon className="w-5 h-5" />
                            </motion.button>
                          </div>
                        </div>

                        {/* File Info */}
                        <div className="p-4">
                          <h3 className="font-medium text-gray-900 truncate mb-1">
                            {file.originalName}
                          </h3>
                          <div className="flex items-center justify-between text-sm text-gray-600">
                            <span>{formatFileSize(file.size)}</span>
                            {file.usageCount > 0 && (
                              <span className="text-blue-600 font-medium">
                                Used {file.usageCount}x
                              </span>
                            )}
                          </div>
                          {file.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {file.tags.slice(0, 2).map(tag => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                              {file.tags.length > 2 && (
                                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                                  +{file.tags.length - 2}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )
                  })}
                </AnimatePresence>
              </div>
            )}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-8">
                <div className="text-sm text-gray-600">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} files
                </div>

                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={!pagination.hasPrev}
                    className="px-4 py-2 text-sm font-medium text-gray-600 bg-white/60 border border-gray-200 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Previous
                  </motion.button>

                  <span className="px-4 py-2 text-sm font-medium text-gray-900">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={!pagination.hasNext}
                    className="px-4 py-2 text-sm font-medium text-gray-600 bg-white/60 border border-gray-200 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next
                  </motion.button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
