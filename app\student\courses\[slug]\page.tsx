'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'

import { toast } from 'react-hot-toast'
import { getVideoAnalytics } from '@/lib/video-analytics'
import {
  PlayIcon,
  ArrowLeftIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  DocumentIcon,
  ClipboardDocumentListIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  StarIcon,
  UsersIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid'
import Link from 'next/link'

// Dynamic import for video player to avoid SSR issues
const BunnyVideoPlayer = dynamic(() => import('@/components/video/bunny-video-player'), {
  ssr: false,
  loading: () => (
    <div className="aspect-video bg-gray-900 rounded-xl flex items-center justify-center">
      <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin" />
    </div>
  )
})

// Dynamic import for certificate modal
const CertificateModal = dynamic(() => import('@/components/student/certificate-modal'), {
  ssr: false
})

interface CourseLesson {
  id: string
  title: string
  description?: string
  content?: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration?: number
  isPublished: boolean
  isFree: boolean
  attachments?: any[]
  quizId?: string
  video?: {
    id: string
    url: string
    duration?: number
  }
}

interface CourseChapter {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  lessons: CourseLesson[]
}

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  chapters: CourseChapter[]
}

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  category?: string
  level?: string
  thumbnailImage?: string
  rating?: number
  studentsCount?: number
  instructor: string | {
    id: string
    name: string
    image?: string
  }
  sections?: CourseSection[]  // Made optional to handle missing sections
  totalLessons?: number
  totalDuration?: number
  isEnrolled?: boolean
}

interface LessonProgress {
  id: string
  lessonId: string
  isCompleted: boolean
  watchTime: number
  lastPosition: number
  completedAt?: string
  lastAccessAt: string
}

interface CourseProgress {
  courseId: string
  progress: LessonProgress[]
  statistics: {
    totalLessons: number
    completedLessons: number
    totalWatchTime: number
    completionPercentage: number
    lastAccessedAt?: number
  }
}

export default function StudentCoursePage() {
  const params = useParams()
  const router = useRouter()
  const courseSlug = params?.slug as string

  const [course, setCourse] = useState<Course | null>(null)
  const [courseProgress, setCourseProgress] = useState<CourseProgress | null>(null)
  const [currentLesson, setCurrentLesson] = useState<CourseLesson | null>(null)
  const [loading, setLoading] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [showCertificateModal, setShowCertificateModal] = useState(false)
  const [previousCompletionPercentage, setPreviousCompletionPercentage] = useState(0)

  useEffect(() => {
    if (courseSlug) {
      fetchCourse()
      fetchProgress()
    }
  }, [courseSlug])

  // Track completion percentage changes
  useEffect(() => {
    if (courseProgress?.statistics?.completionPercentage !== undefined) {
      setPreviousCompletionPercentage(courseProgress.statistics.completionPercentage)
    }
  }, [courseProgress?.statistics?.completionPercentage])

  const fetchCourse = async () => {
    try {

      const response = await fetch(`/api/student/courses/${courseSlug}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Course fetch failed:', response.status, response.statusText, errorData)
        throw new Error(errorData.message || `Failed to fetch course: ${response.status}`)
      }

      const data = await response.json()


      // Handle different API response structures
      const courseData = data.course || data.data?.course || data


      if (!courseData || !courseData.id) {
        throw new Error('Invalid course data received from API')
      }

      setCourse(courseData)

      // Set course progress if available
      if (data.courseProgress) {

        setCourseProgress(data.courseProgress)
      }

      // Set first lesson as current if no lesson is selected and sections exist
      if (courseData && courseData.sections && courseData.sections.length > 0) {
        const firstSection = courseData.sections[0]
        if (firstSection && firstSection.chapters && firstSection.chapters.length > 0) {
          const firstChapter = firstSection.chapters[0]
          if (firstChapter && firstChapter.lessons && firstChapter.lessons.length > 0) {
            setCurrentLesson(firstChapter.lessons[0])
          }
        }
      }

      // Expand all sections by default if they exist
      if (courseData && courseData.sections) {
        const sectionIds = new Set<string>(courseData.sections.map((s: CourseSection) => s.id as string))
        setExpandedSections(sectionIds)
      }

      // Set loading to false since we got the data
      setLoading(false)
    } catch (error: any) {
      console.error('Error fetching course:', error)
      toast.error(error.message || 'Failed to load course')
      router.push('/student/courses')
    }
  }

  const fetchProgress = async () => {
    try {
      const response = await fetch(`/api/student/courses/progress?courseSlug=${courseSlug}`)
      if (!response.ok) throw new Error('Failed to fetch progress')

      const data = await response.json()

      setCourseProgress(data)
    } catch (error) {
      console.error('Error fetching progress:', error)

      // Initialize with empty progress to prevent undefined errors
      setCourseProgress({
        courseId: courseSlug || '',
        progress: [],
        statistics: {
          totalLessons: 0,
          completedLessons: 0,
          completionPercentage: 0,
          totalWatchTime: 0
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const updateLessonProgress = async (lessonId: string, progressData: {
    watchTime?: number
    lastPosition?: number
    isCompleted?: boolean
  }) => {
    try {
      const response = await fetch('/api/student/courses/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId,
          ...progressData
        })
      })

      if (!response.ok) throw new Error('Failed to update progress')

      // Refresh progress data
      fetchProgress()
    } catch (error) {
      console.error('Error updating progress:', error)
    }
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const selectLesson = (lesson: CourseLesson) => {
    setCurrentLesson(lesson)
    // Mark lesson as accessed
    updateLessonProgress(lesson.id, {})

    // Start video analytics session if it's a video lesson
    if (lesson.type === 'VIDEO' && lesson.video) {
      const analytics = getVideoAnalytics()
      // End previous session if exists
      analytics.endSession()
      // Start new session
      analytics.startSession(lesson.id, 'current-user-id') // TODO: Get actual user ID
    }
  }

  const markLessonComplete = (lessonId: string) => {
    updateLessonProgress(lessonId, { isCompleted: true })

    // Update local progress state immediately for better UX
    if (courseProgress && courseProgress.progress && Array.isArray(courseProgress.progress) && courseProgress.statistics) {
      const updatedProgress = { ...courseProgress }

      // Ensure progress array exists
      if (!updatedProgress.progress) {
        updatedProgress.progress = []
      }

      const lessonProgressIndex = updatedProgress.progress.findIndex(p => p && p.lessonId === lessonId)

      if (lessonProgressIndex >= 0) {
        updatedProgress.progress[lessonProgressIndex].isCompleted = true
      } else {
        updatedProgress.progress.push({
          id: `temp-${lessonId}`,
          lessonId,
          isCompleted: true,
          watchTime: 0,
          lastPosition: 0,
          lastAccessAt: new Date().toISOString()
        })
      }

      // Recalculate statistics with null checks
      const completedLessons = updatedProgress.progress.filter(p => p && p.isCompleted).length
      const totalLessons = updatedProgress.statistics?.totalLessons || 0
      const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

      updatedProgress.statistics = {
        ...updatedProgress.statistics,
        completedLessons,
        completionPercentage
      }

      // Check if course just reached 100% completion
      const wasCompleted = previousCompletionPercentage === 100
      const isNowCompleted = completionPercentage === 100

      if (!wasCompleted && isNowCompleted) {
        // Course just completed! Show certificate modal after a short delay
        setTimeout(() => {
          setShowCertificateModal(true)
          toast.success('🎉 Congratulations! You have completed the course!')
        }, 1000)
      }

      setPreviousCompletionPercentage(completionPercentage)
      setCourseProgress(updatedProgress)
    } else {
      // If courseProgress is not properly initialized, just update the lesson progress
      console.warn('Course progress not properly initialized, skipping local state update')
    }
  }

  const getLessonProgress = (lessonId: string): LessonProgress | undefined => {
    if (!courseProgress || !Array.isArray(courseProgress.progress)) {

      return undefined
    }
    return courseProgress.progress.find(p => p && p.lessonId === lessonId)
  }

  const isLessonCompleted = (lessonId: string): boolean => {
    if (!courseProgress || !Array.isArray(courseProgress.progress)) {
      return false
    }
    const progress = getLessonProgress(lessonId)
    return progress?.isCompleted || false
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'DOCUMENT':
        return <DocumentIcon className="w-4 h-4" />
      case 'ASSIGNMENT':
        return <ClipboardDocumentListIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl lg:text-2xl font-bold text-gray-800 dark:text-white mb-2">Course not found</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 text-sm lg:text-base">The course you're looking for doesn't exist or you don't have access to it.</p>
          <Link href="/student/courses">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 lg:px-6 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 text-sm lg:text-base"
            >
              Back to Courses
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-white/20 dark:border-slate-700/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 lg:py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <Link href="/student/courses">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5" />
                </motion.button>
              </Link>
              <div className="min-w-0 flex-1">
                <h1 className="text-lg lg:text-xl font-bold text-gray-800 dark:text-white truncate">{course.title}</h1>
                <div className="flex flex-wrap items-center gap-2 lg:gap-4 text-xs lg:text-sm text-gray-600 dark:text-gray-300 mt-1">
                  <span className="flex items-center">
                    <UsersIcon className="w-3 lg:w-4 h-3 lg:h-4 mr-1" />
                    <span className="hidden sm:inline">{course.studentsCount || 0} students</span>
                    <span className="sm:hidden">{course.studentsCount || 0}</span>
                  </span>
                  {course.rating && (
                    <span className="flex items-center">
                      <StarIcon className="w-3 lg:w-4 h-3 lg:h-4 mr-1 text-yellow-400 fill-current" />
                      {course.rating.toFixed(1)}
                    </span>
                  )}
                  <span className="hidden sm:inline">{course.level}</span>
                </div>
              </div>
            </div>

            {/* Enhanced Progress Indicator */}
            {courseProgress && courseProgress.statistics && (
              <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/50 p-4 lg:p-6 mb-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  {/* Progress Stats */}
                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <div className="text-2xl lg:text-3xl font-bold text-blue-600 dark:text-blue-400">
                        {Math.round(courseProgress.statistics.completionPercentage || 0)}%
                      </div>
                      <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300 font-medium">
                        Complete
                      </div>
                    </div>
                    <div className="h-12 w-px bg-gray-200 dark:bg-slate-600"></div>
                    <div className="text-center">
                      <div className="text-lg lg:text-xl font-semibold text-gray-800 dark:text-white">
                        {courseProgress.statistics.completedLessons || 0}/{courseProgress.statistics.totalLessons || 0}
                      </div>
                      <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300 font-medium">
                        Lessons
                      </div>
                    </div>
                    {courseProgress.statistics.totalWatchTime > 0 && (
                      <>
                        <div className="h-12 w-px bg-gray-200 dark:bg-slate-600"></div>
                        <div className="text-center">
                          <div className="text-lg lg:text-xl font-semibold text-gray-800 dark:text-white">
                            {Math.round(courseProgress.statistics.totalWatchTime / 3600)}h
                          </div>
                          <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300 font-medium">
                            Watched
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Progress Bar */}
                  <div className="flex-1 lg:max-w-md">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Course Progress
                      </span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {Math.round(courseProgress.statistics.completionPercentage || 0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-3 shadow-inner">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${courseProgress.statistics.completionPercentage || 0}%` }}
                        transition={{ duration: 1, ease: "easeOut" }}
                        className="bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 h-3 rounded-full shadow-sm relative overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                      </motion.div>
                    </div>
                    {courseProgress.statistics.completionPercentage === 100 && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex flex-col items-center justify-center mt-2 space-y-2"
                      >
                        <div className="flex items-center text-green-600 dark:text-green-400">
                          <CheckCircleIconSolid className="w-4 h-4 mr-1" />
                          <span className="text-sm font-medium">Course Completed!</span>
                        </div>
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setShowCertificateModal(true)}
                          className="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs rounded-full font-medium shadow-sm hover:shadow-md transition-all duration-200"
                        >
                          🏆 Get Certificate
                        </motion.button>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            {currentLesson ? (
              <LessonPlayer
                lesson={currentLesson}
                progress={getLessonProgress(currentLesson.id)}
                onProgressUpdate={(progressData) => updateLessonProgress(currentLesson.id, progressData)}
                onComplete={() => markLessonComplete(currentLesson.id)}
              />
            ) : (
              <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8 text-center">
                <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Welcome to the Course</h3>
                <p className="text-gray-600 mb-6">Select a lesson from the sidebar to start learning</p>
              </div>
            )}
          </div>

          {/* Course Sidebar */}
          <div className="lg:col-span-1">
            <CourseSidebar
              course={course}
              currentLesson={currentLesson}
              expandedSections={expandedSections}
              onToggleSection={toggleSection}
              onSelectLesson={selectLesson}
              getLessonProgress={getLessonProgress}
              isLessonCompleted={isLessonCompleted}
            />
          </div>
        </div>
      </div>

      {/* Certificate Modal */}
      {course && (
        <CertificateModal
          isOpen={showCertificateModal}
          onClose={() => setShowCertificateModal(false)}
          courseId={course.id}
          courseName={course.title}
          instructorName={typeof course.instructor === 'string' ? course.instructor : course.instructor.name}
        />
      )}
    </div>
  )
}

interface LessonPlayerProps {
  lesson: CourseLesson
  progress?: LessonProgress
  onProgressUpdate: (progressData: { watchTime?: number; lastPosition?: number; isCompleted?: boolean }) => void
  onComplete: () => void
}

function LessonPlayer({ lesson, progress, onProgressUpdate, onComplete }: LessonPlayerProps) {
  const [videoCurrentTime, setVideoCurrentTime] = useState(0)
  const [videoDuration, setVideoDuration] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'DOCUMENT':
        return <DocumentIcon className="w-4 h-4" />
      case 'ASSIGNMENT':
        return <ClipboardDocumentListIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const handleVideoTimeUpdate = (currentTime: number) => {
    setVideoCurrentTime(currentTime)

    // Update progress every 10 seconds
    if (Math.floor(currentTime) % 10 === 0) {
      onProgressUpdate({
        watchTime: Math.floor(currentTime),
        lastPosition: Math.floor(currentTime)
      })
    }

    // Mark as complete when 90% watched
    if (videoDuration > 0 && currentTime / videoDuration >= 0.9 && !progress?.isCompleted) {
      onProgressUpdate({ isCompleted: true })
      onComplete()
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden"
    >
      {/* Lesson Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">{lesson.title}</h2>
            {lesson.description && (
              <p className="text-gray-600">{lesson.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center text-sm text-gray-600">
              {getLessonIcon(lesson.type)}
              <span className="ml-2">{lesson.type}</span>
            </div>
            {progress?.isCompleted && (
              <CheckCircleIconSolid className="w-6 h-6 text-green-500" />
            )}
          </div>
        </div>
      </div>

      {/* Lesson Content */}
      <div className="p-6">
        {lesson.type === 'VIDEO' && lesson.video ? (
          <div className="space-y-4">
            <BunnyVideoPlayer
              videoUrl={lesson.video.url}
              posterUrl={lesson.video.thumbnailUrl || undefined}
              title={lesson.title}
              startTime={progress?.lastPosition || 0}
              onTimeUpdate={handleVideoTimeUpdate}
              onProgress={(watchTime, percentage) => {
                onProgressUpdate({
                  watchTime,
                  lastPosition: watchTime
                })
              }}
              onComplete={() => {
                onProgressUpdate({ isCompleted: true })
                onComplete()
              }}
              className="w-full"
            />

            {/* Video Progress */}
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{formatTime(videoCurrentTime)} / {formatTime(videoDuration)}</span>
              <div className="flex items-center space-x-2">
                {progress?.watchTime && (
                  <span>Watch time: {formatTime(progress.watchTime)}</span>
                )}
                {progress?.isCompleted && (
                  <span className="flex items-center text-green-600">
                    <CheckCircleIconSolid className="w-4 h-4 mr-1" />
                    Completed
                  </span>
                )}
              </div>
            </div>
          </div>
        ) : lesson.type === 'TEXT' ? (
          <div className="prose max-w-none">
            <div className="bg-white rounded-xl p-8 shadow-lg">
              {lesson.content ? (
                <div
                  className="text-gray-800 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: lesson.content }}
                />
              ) : (
                <p className="text-gray-600 mb-4">No content available for this lesson.</p>
              )}

              {/* Attachments */}
              {lesson.attachments && lesson.attachments.length > 0 && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4">Attachments</h4>
                  <div className="space-y-3">
                    {lesson.attachments.map((attachment: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <DocumentIcon className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="text-sm font-medium text-gray-800">
                              {attachment.originalName || attachment.name}
                            </p>
                            {attachment.size && (
                              <p className="text-xs text-gray-500">
                                {(attachment.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            )}
                          </div>
                        </div>
                        <a
                          href={attachment.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Download
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-8 pt-6 border-t border-gray-200">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    onProgressUpdate({ isCompleted: true })
                    onComplete()
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  Mark as Complete
                </motion.button>
              </div>
            </div>
          </div>
        ) : lesson.type === 'QUIZ' ? (
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 text-center border border-blue-200">
            <AcademicCapIcon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Quiz Lesson</h3>
            <div className="text-gray-600 mb-4">
              {lesson.content ? (
                <div className="prose prose-sm max-w-none">
                  <p>{lesson.content}</p>
                </div>
              ) : (
                <p>Test your understanding with this interactive quiz.</p>
              )}
            </div>
            {lesson.quizId ? (
              <Link href={`/student/quiz/${lesson.quizId}`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  Start Quiz
                </motion.button>
              </Link>
            ) : (
              <div className="text-gray-500 text-sm">
                Quiz not yet configured for this lesson.
              </div>
            )}
          </div>
        ) : lesson.type === 'DOCUMENT' ? (
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="text-center mb-6">
              <DocumentIcon className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Document Lesson</h3>
              {lesson.description && (
                <p className="text-gray-600">{lesson.description}</p>
              )}
            </div>

            {lesson.content && (
              <div
                className="text-gray-800 leading-relaxed mb-6"
                dangerouslySetInnerHTML={{ __html: lesson.content }}
              />
            )}

            {/* Document Attachments */}
            {lesson.attachments && lesson.attachments.length > 0 && (
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">Documents</h4>
                <div className="grid gap-4">
                  {lesson.attachments.map((attachment: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                      <div className="flex items-center space-x-4">
                        <DocumentIcon className="w-8 h-8 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-800">
                            {attachment.originalName || attachment.name}
                          </p>
                          {attachment.size && (
                            <p className="text-sm text-gray-500">
                              {(attachment.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          )}
                        </div>
                      </div>
                      <a
                        href={attachment.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        View Document
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="pt-6 border-t border-gray-200">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  onProgressUpdate({ isCompleted: true })
                  onComplete()
                }}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Mark as Complete
              </motion.button>
            </div>
          </div>
        ) : lesson.type === 'ASSIGNMENT' ? (
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="text-center mb-6">
              <ClipboardDocumentListIcon className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Assignment</h3>
              {lesson.description && (
                <p className="text-gray-600">{lesson.description}</p>
              )}
            </div>

            {lesson.content && (
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-3">Instructions</h4>
                <div
                  className="text-gray-800 leading-relaxed p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400"
                  dangerouslySetInnerHTML={{ __html: lesson.content }}
                />
              </div>
            )}

            {/* Assignment Resources */}
            {lesson.attachments && lesson.attachments.length > 0 && (
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">Assignment Resources</h4>
                <div className="space-y-3">
                  {lesson.attachments.map((attachment: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <DocumentIcon className="w-5 h-5 text-green-600" />
                        <div>
                          <p className="text-sm font-medium text-gray-800">
                            {attachment.originalName || attachment.name}
                          </p>
                          {attachment.size && (
                            <p className="text-xs text-gray-500">
                              {(attachment.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          )}
                        </div>
                      </div>
                      <a
                        href={attachment.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        Download
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="pt-6 border-t border-gray-200">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  onProgressUpdate({ isCompleted: true })
                  onComplete()
                }}
                className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Submit Assignment
              </motion.button>
            </div>
          </div>
        ) : (
          <div className="bg-gray-50 rounded-xl p-6 text-center">
            <DocumentTextIcon className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Lesson Content</h3>
            <p className="text-gray-600">This lesson type is not yet supported.</p>
          </div>
        )}
      </div>
    </motion.div>
  )
}

interface CourseSidebarProps {
  course: Course
  currentLesson: CourseLesson | null
  expandedSections: Set<string>
  onToggleSection: (sectionId: string) => void
  onSelectLesson: (lesson: CourseLesson) => void
  getLessonProgress: (lessonId: string) => LessonProgress | undefined
  isLessonCompleted: (lessonId: string) => boolean
}

function CourseSidebar({
  course,
  currentLesson,
  expandedSections,
  onToggleSection,
  onSelectLesson,
  getLessonProgress,
  isLessonCompleted
}: CourseSidebarProps) {
  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'DOCUMENT':
        return <DocumentIcon className="w-4 h-4" />
      case 'ASSIGNMENT':
        return <ClipboardDocumentListIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  return (
    <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden">
      {/* Course Info */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3 mb-4">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-12 h-12 rounded-lg object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <AcademicCapIcon className="w-6 h-6 text-white" />
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-800">
              {typeof course.instructor === 'string' ? course.instructor : course.instructor?.name || 'Instructor'}
            </h3>
            <p className="text-sm text-gray-600">Instructor</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="font-medium text-gray-800">{course.totalLessons}</div>
            <div className="text-gray-600">Lessons</div>
          </div>
          <div>
            <div className="font-medium text-gray-800">{formatDuration((course.totalDuration || 0) * 60)}</div>
            <div className="text-gray-600">Duration</div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-h-96 overflow-y-auto">
        {(!course.sections || course.sections.length === 0) ? (
          <div className="p-8 text-center text-gray-500">
            <BookOpenIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No course content available yet.</p>
            <p className="text-sm mt-2">The instructor is still preparing the course materials.</p>
          </div>
        ) : (
          (course.sections || []).map((section) => (
          <div key={section.id} className="border-b border-gray-200 last:border-b-0">
            {/* Section Header */}
            <button
              onClick={() => onToggleSection(section.id)}
              className="w-full p-4 text-left hover:bg-gray-50/50 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {expandedSections.has(section.id) ? (
                    <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                  ) : (
                    <ChevronRightIcon className="w-4 h-4 text-gray-500" />
                  )}
                  <span className="font-medium text-gray-800">{section.title}</span>
                </div>
                <span className="text-xs text-gray-500">
                  {section.chapters.reduce((acc, chapter) => acc + chapter.lessons.length, 0)} lessons
                </span>
              </div>
            </button>

            {/* Section Content */}
            <AnimatePresence>
              {expandedSections.has(section.id) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  {section.chapters.map((chapter) => (
                    <div key={chapter.id} className="pl-4">
                      {chapter.lessons.map((lesson) => {
                        const isCompleted = isLessonCompleted(lesson.id)
                        const isCurrent = currentLesson?.id === lesson.id

                        return (
                          <motion.button
                            key={lesson.id}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                            onClick={() => onSelectLesson(lesson)}
                            className={`w-full p-3 text-left border-l-2 transition-all duration-200 ${
                              isCurrent
                                ? 'border-blue-500 bg-blue-50/50'
                                : 'border-transparent hover:bg-gray-50/50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="text-gray-600">
                                  {getLessonIcon(lesson.type)}
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-800">
                                    {lesson.title}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {lesson.type} • {formatDuration(lesson.duration)}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                {lesson.isFree && (
                                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    Free
                                  </span>
                                )}
                                {isCompleted && (
                                  <CheckCircleIconSolid className="w-5 h-5 text-green-500" />
                                )}
                              </div>
                            </div>
                          </motion.button>
                        )
                      })}
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )))}
      </div>
    </div>
  )
}
